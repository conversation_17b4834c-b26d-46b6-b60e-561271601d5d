"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Navigation } from "@/components/navigation"
import "./globals.css"

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()

  return (
    <html lang="ar" dir="rtl">
      <head>
        <title>مجموعة H - نظام إدارة مصنع الأثاث</title>
        <meta name="description" content="نظام شامل لإدارة الإنتاج والأعمال لتصنيع الأثاث بالدينار الليبي" />
      </head>
      <body>
        {pathname !== "/login" && <Navigation />}
        {children}
      </body>
    </html>
  )
}
