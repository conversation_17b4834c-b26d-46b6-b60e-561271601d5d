"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Eye, EyeOff, Lock, User, Building, Sparkles } from "lucide-react"
import { useRouter } from "next/navigation"

export default function LoginPage() {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  })
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  // Mock users - in real app, this would be handled by backend authentication
  const users = [
    { username: "admin", password: "admin123", role: "مدير عام", name: "أحمد المدير" },
    { username: "manager", password: "manager123", role: "مدير إنتاج", name: "محمد المشرف" },
    { username: "accountant", password: "acc123", role: "محاسب", name: "فاطمة المحاسبة" },
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    setError("")
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const user = users.find((u) => u.username === formData.username && u.password === formData.password)

    if (user) {
      // Store user session (in real app, use proper session management)
      try {
        if (typeof window !== 'undefined') {
          localStorage.setItem("user", JSON.stringify(user))
        }
        router.push("/")
      } catch (error) {
        console.error("Error storing user session:", error)
        setError("حدث خطأ في حفظ بيانات الجلسة")
      }
    } else {
      setError("اسم المستخدم أو كلمة المرور غير صحيحة")
    }

    setIsLoading(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md animate-fade-in">
        {/* Logo and Title */}
        <div className="text-center mb-8">
          <div className="relative inline-block mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
              <Building className="h-10 w-10 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full flex items-center justify-center">
              <Sparkles className="h-3 w-3 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
            مجموعة H
          </h1>
          <p className="text-slate-600 text-lg">نظام إدارة مصنع الأثاث</p>
          <p className="text-slate-500 text-sm mt-2">تسجيل دخول آمن ومحمي</p>
        </div>

        {/* Login Form */}
        <Card className="modern-card border-0 shadow-2xl">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-slate-900 flex items-center justify-center space-x-3 space-x-reverse text-xl">
              <div className="p-2 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-xl">
                <Lock className="h-5 w-5 text-indigo-600" />
              </div>
              <span>تسجيل الدخول</span>
            </CardTitle>
            <CardDescription className="text-slate-600">أدخل بيانات الدخول للوصول إلى النظام</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <form onSubmit={handleLogin} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="username" className="text-slate-700 font-medium">
                  اسم المستخدم
                </Label>
                <div className="relative">
                  <User className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange("username", e.target.value)}
                    placeholder="أدخل اسم المستخدم"
                    className="modern-input pr-12 h-12 text-lg"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-slate-700 font-medium">
                  كلمة المرور
                </Label>
                <div className="relative">
                  <Lock className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    placeholder="أدخل كلمة المرور"
                    className="modern-input pr-12 pl-12 h-12 text-lg"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              {error && (
                <Alert className="border-red-200 bg-red-50 animate-fade-in">
                  <AlertDescription className="text-red-800">{error}</AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                disabled={isLoading}
                className="modern-btn-primary w-full h-12 text-lg disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>جاري تسجيل الدخول...</span>
                  </div>
                ) : (
                  "تسجيل الدخول"
                )}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="mt-8 p-6 bg-gradient-to-r from-slate-50 to-slate-100 rounded-2xl border border-slate-200">
              <h4 className="font-semibold text-slate-900 mb-4 flex items-center space-x-2 space-x-reverse">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <span>بيانات تجريبية</span>
              </h4>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center p-3 bg-white rounded-xl border border-slate-200">
                  <span className="font-medium text-slate-700">مدير عام</span>
                  <span className="text-slate-500">admin / admin123</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-white rounded-xl border border-slate-200">
                  <span className="font-medium text-slate-700">مدير إنتاج</span>
                  <span className="text-slate-500">manager / manager123</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-white rounded-xl border border-slate-200">
                  <span className="font-medium text-slate-700">محاسب</span>
                  <span className="text-slate-500">accountant / acc123</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 space-y-2">
          <p className="text-slate-500 text-sm">جميع الحقوق محفوظة © 2024 مجموعة H</p>
          <p className="text-slate-400 text-xs">نظام إدارة مصنع الأثاث - الدينار الليبي (د.ل)</p>
        </div>
      </div>
    </div>
  )
}
