// Check if we're in Tauri environment
const isTauri = () => {
  if (typeof window === 'undefined') return false;
  return !!(window as any).__TAURI__ || !!(window as any).__TAURI_INTERNALS__;
};

// Dynamic imports for Tauri APIs
const getTauriApi = async () => {
  if (!isTauri()) {
    console.log("Not in Tauri environment, using fallback mode");
    return null;
  }

  try {
    const { invoke } = await import("@tauri-apps/api/core");
    const { save, open } = await import("@tauri-apps/api/dialog");
    const { writeTextFile, readTextFile } = await import("@tauri-apps/api/fs");
    const { sendNotification } = await import("@tauri-apps/api/notification");

    console.log("Tauri APIs loaded successfully");
    return {
      invoke,
      save,
      open,
      writeTextFile,
      readTextFile,
      notification: { sendNotification }
    };
  } catch (error) {
    console.warn("Tauri APIs not available:", error);
    return null;
  }
};

// Types
export interface Material {
  id: number
  name: string
  category: string
  current_stock: number
  min_stock: number
  max_stock: number
  unit: string
  cost_per_unit: number
  supplier: string
  last_restocked: string
  created_at: string
  updated_at: string
}

export interface CreateMaterial {
  name: string
  category: string
  current_stock: number
  min_stock: number
  max_stock: number
  unit: string
  cost_per_unit: number
  supplier: string
}

export interface Customer {
  id: number
  name: string
  email?: string
  phone?: string
  address?: string
  customer_type: string
  status: string
  total_projects: number
  total_spent: number
  discount: number
  discount_reason?: string
  registration_date: string
  last_interaction: string
  created_at: string
  updated_at: string
}

export interface CreateCustomer {
  name: string
  email?: string
  phone?: string
  address?: string
  customer_type: string
}

export interface DashboardStats {
  total_projects: number
  active_projects: number
  total_employees: number
  total_customers: number
  monthly_revenue: number
  monthly_expenses: number
  total_materials: number
  low_stock_materials: number
  pending_payments: number
  completed_projects_this_month: number
}

export interface RecentActivity {
  id: number
  title: string
  description: string
  activity_type: string
  created_at: string
}

// Materials API
export const materialsApi = {
  getAll: async (): Promise<Material[]> => {
    const api = await getTauriApi();
    if (!api) return [];
    return api.invoke("get_materials");
  },
  create: async (material: CreateMaterial): Promise<Material> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("create_material", { material });
  },
  update: async (id: number, material: CreateMaterial): Promise<Material> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("update_material", { id, material });
  },
  delete: async (id: number): Promise<boolean> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("delete_material", { id });
  },
  import: async (materials: CreateMaterial[]): Promise<Material[]> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("import_materials", { materials });
  },
  export: async (): Promise<string> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("export_materials");
  },
}

// Customers API
export const customersApi = {
  getAll: async (): Promise<Customer[]> => {
    const api = await getTauriApi();
    if (!api) return [];
    return api.invoke("get_customers");
  },
  getById: async (id: number): Promise<Customer | null> => {
    const api = await getTauriApi();
    if (!api) return null;
    return api.invoke("get_customer_by_id", { id });
  },
  create: async (customer: CreateCustomer): Promise<Customer> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("create_customer", { customer });
  },
  update: async (id: number, customer: CreateCustomer): Promise<Customer> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("update_customer", { id, customer });
  },
  delete: async (id: number): Promise<boolean> => {
    const api = await getTauriApi();
    if (!api) throw new Error("Tauri not available");
    return api.invoke("delete_customer", { id });
  },
}

// Dashboard API
export const dashboardApi = {
  getStats: async (): Promise<DashboardStats> => {
    const api = await getTauriApi();
    if (!api) {
      // بيانات تجريبية للمتصفح
      return {
        total_projects: 15,
        active_projects: 8,
        total_employees: 12,
        total_customers: 25,
        monthly_revenue: 45750,
        monthly_expenses: 28500,
        total_materials: 35,
        low_stock_materials: 5,
        pending_payments: 12300,
        completed_projects_this_month: 7,
      };
    }
    return api.invoke("get_dashboard_stats");
  },
  getRecentActivities: async (): Promise<RecentActivity[]> => {
    const api = await getTauriApi();
    if (!api) {
      // بيانات تجريبية للمتصفح
      return [
        {
          id: 1,
          title: "تم إضافة عميل جديد: شركة الأثاث الحديث",
          description: "عميل جديد مع مشروع أثاث مكتبي",
          activity_type: "customer",
          created_at: new Date(Date.now() - 3600000).toISOString(), // منذ ساعة
        },
        {
          id: 2,
          title: "انتقال فاتورة إلى مرحلة التصنيع",
          description: "تم تسجيل دفعة 70% للمشروع #001",
          activity_type: "payment",
          created_at: new Date(Date.now() - 7200000).toISOString(), // منذ ساعتين
        },
        {
          id: 3,
          title: "تم تخصيص مشروع جديد للموظف أحمد محمد",
          description: "مشروع أثاث غرفة نوم",
          activity_type: "project",
          created_at: new Date(Date.now() - 10800000).toISOString(), // منذ 3 ساعات
        },
        {
          id: 4,
          title: "تم إكمال طلب الإنتاج #PO-2024-001",
          description: "مشروع أثاث مطبخ مكتمل",
          activity_type: "project",
          created_at: new Date(Date.now() - 14400000).toISOString(), // منذ 4 ساعات
        },
        {
          id: 5,
          title: "تحديث مخزون المواد الخام",
          description: "إضافة 50 متر من الخشب الزان",
          activity_type: "material",
          created_at: new Date(Date.now() - 18000000).toISOString(), // منذ 5 ساعات
        },
      ];
    }
    return api.invoke("get_recent_activities");
  },
}

// Activities API
export const activitiesApi = {
  getRecent: async (): Promise<RecentActivity[]> => {
    const api = await getTauriApi();
    if (!api) {
      // استخدم نفس البيانات من dashboardApi
      return dashboardApi.getRecentActivities();
    }
    return api.invoke("get_recent_activities");
  },
}

// File operations
export const fileApi = {
  saveFile: async (content: string, defaultName: string, filters?: any[]) => {
    const api = await getTauriApi();
    if (!api) {
      console.warn("File save not available in web mode");
      return null;
    }

    const filePath = await api.save({
      defaultPath: defaultName,
      filters: filters || [
        { name: "CSV Files", extensions: ["csv"] },
        { name: "All Files", extensions: ["*"] },
      ],
    })

    if (filePath) {
      await api.writeTextFile(filePath, content)
      await api.notification.sendNotification({
        title: "تم الحفظ بنجاح",
        body: `تم حفظ الملف في: ${filePath}`,
      })
    }

    return filePath
  },

  openFile: async (filters?: any[]) => {
    const api = await getTauriApi();
    if (!api) {
      console.warn("File open not available in web mode");
      return null;
    }

    const filePath = await api.open({
      multiple: false,
      filters: filters || [
        { name: "CSV Files", extensions: ["csv"] },
        { name: "Excel Files", extensions: ["xlsx", "xls"] },
        { name: "All Files", extensions: ["*"] },
      ],
    })

    if (filePath && typeof filePath === "string") {
      const content = await api.readTextFile(filePath)
      return { path: filePath, content }
    }

    return null
  },
}

// Notifications
export const notificationApi = {
  success: async (title: string, body: string) => {
    const api = await getTauriApi();
    if (!api) {
      console.log(`Success: ${title} - ${body}`);
      return;
    }
    api.notification.sendNotification({
      title,
      body,
      icon: "success",
    })
  },

  error: async (title: string, body: string) => {
    const api = await getTauriApi();
    if (!api) {
      console.error(`Error: ${title} - ${body}`);
      return;
    }
    api.notification.sendNotification({
      title,
      body,
      icon: "error",
    })
  },

  info: async (title: string, body: string) => {
    const api = await getTauriApi();
    if (!api) {
      console.info(`Info: ${title} - ${body}`);
      return;
    }
    api.notification.sendNotification({
      title,
      body,
      icon: "info",
    })
  },
}

// Error handling wrapper
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  errorMessage: string = 'حدث خطأ غير متوقع'
): Promise<T | null> => {
  try {
    return await operation()
  } catch (error) {
    console.error("API Error:", error)
    await notificationApi.error("خطأ", `${errorMessage}: ${error}`)
    return null
  }
}
