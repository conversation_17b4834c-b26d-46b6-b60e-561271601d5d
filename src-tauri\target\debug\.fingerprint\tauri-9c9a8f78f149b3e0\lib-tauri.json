{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 15510866022933256768, "deps": [[40386456601120721, "percent_encoding", false, 13569763904683352969], [442785307232013896, "tauri_runtime", false, 12737817405569267810], [1200537532907108615, "url<PERSON><PERSON>n", false, 635099364069274947], [3150220818285335163, "url", false, 8378097199966561882], [4143744114649553716, "raw_window_handle", false, 3760476461987929444], [4341921533227644514, "muda", false, 13750291126707606692], [4919829919303820331, "serialize_to_javascript", false, 6110108274067054794], [5986029879202738730, "log", false, 14415701566817692790], [7752760652095876438, "tauri_runtime_wry", false, 9174073248826448671], [8351317599104215083, "tray_icon", false, 985866961938738332], [8539587424388551196, "webview2_com", false, 2612316728963077255], [9010263965687315507, "http", false, 12211119702532877820], [9228235415475680086, "tauri_macros", false, 3777520894572266134], [9538054652646069845, "tokio", false, 13844825867709392792], [9689903380558560274, "serde", false, 8732212302608731374], [9920160576179037441, "getrandom", false, 10803064505267815418], [10229185211513642314, "mime", false, 8769157950524531158], [10629569228670356391, "futures_util", false, 5575848102711976048], [10755362358622467486, "build_script_build", false, 2638019374577580987], [10806645703491011684, "thiserror", false, 1911401726845299826], [11050281405049894993, "tauri_utils", false, 15157767861222970160], [11989259058781683633, "dunce", false, 6350847957934485460], [12565293087094287914, "window_vibrancy", false, 7230954963491808836], [12986574360607194341, "serde_repr", false, 11233520184535608381], [13077543566650298139, "heck", false, 15917200172082812446], [13116089016666501665, "windows", false, 4842511405203577448], [13625485746686963219, "anyhow", false, 2915015514010170155], [15367738274754116744, "serde_json", false, 6491859613021625460], [16928111194414003569, "dirs", false, 5814105996255424424], [17155886227862585100, "glob", false, 2004219966905067460]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-9c9a8f78f149b3e0\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}