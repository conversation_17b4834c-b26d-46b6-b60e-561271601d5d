/** @type {import('next').NextConfig} */
const nextConfig = {
  // Remove output: "export" for development mode
  ...(process.env.NODE_ENV === "production" && { output: "export" }),
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
  assetPrefix: process.env.NODE_ENV === "production" ? "./" : "",
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
}

module.exports = nextConfig
