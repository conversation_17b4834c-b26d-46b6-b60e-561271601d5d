{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 4715356481362710911, "deps": [[3060637413840920116, "proc_macro2", false, 7266838655266134858], [7341521034400937459, "tauri_codegen", false, 13559097197331116510], [11050281405049894993, "tauri_utils", false, 3819907218129246676], [13077543566650298139, "heck", false, 15917200172082812446], [17990358020177143287, "quote", false, 17335616060049607687], [18149961000318489080, "syn", false, 17580270427653760500]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-4779f0d5858e006f\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}