use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Material {
    pub id: i64,
    pub name: String,
    pub category: String,
    pub current_stock: f64,
    pub min_stock: f64,
    pub max_stock: f64,
    pub unit: String,
    pub cost_per_unit: f64,
    pub supplier: String,
    pub last_restocked: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateMaterial {
    pub name: String,
    pub category: String,
    pub current_stock: f64,
    pub min_stock: f64,
    pub max_stock: f64,
    pub unit: String,
    pub cost_per_unit: f64,
    pub supplier: String,
}

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Customer {
    pub id: i64,
    pub name: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    pub customer_type: String,
    pub status: String,
    pub total_projects: i32,
    pub total_spent: f64,
    pub discount: f64,
    pub discount_reason: Option<String>,
    pub registration_date: DateTime<Utc>,
    pub last_interaction: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateCustomer {
    pub name: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    pub customer_type: String,
}

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Employee {
    pub id: i64,
    pub name: String,
    pub position: String,
    pub department: String,
    pub hourly_rate: f64,
    pub hours_worked: f64,
    pub overtime: f64,
    pub status: String,
    pub vacation_days: i32,
    pub allowances: f64,
    pub deductions: f64,
    pub social_insurance: f64,
    pub discount: f64,
    pub discount_reason: Option<String>,
    pub join_date: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateEmployee {
    pub name: String,
    pub position: String,
    pub department: String,
    pub hourly_rate: f64,
    pub allowances: f64,
    pub deductions: f64,
}

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Project {
    pub id: i64,
    pub name: String,
    pub customer_id: i64,
    pub assigned_employee_id: Option<i64>,
    pub furniture_type: String,
    pub square_meters: f64,
    pub status: String,
    pub stage: String,
    pub total_amount: f64,
    pub first_payment: f64,
    pub final_payment: f64,
    pub materials_cost: f64,
    pub labor_cost: f64,
    pub designer_fee: f64,
    pub factory_overhead: f64,
    pub profit_margin: f64,
    pub start_date: DateTime<Utc>,
    pub expected_completion: DateTime<Utc>,
    pub actual_completion: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProject {
    pub name: String,
    pub customer_id: i64,
    pub assigned_employee_id: Option<i64>,
    pub furniture_type: String,
    pub square_meters: f64,
    pub materials: Vec<ProjectMaterial>,
    pub accessories: Vec<ProjectAccessory>,
    pub designer_fee: f64,
    pub factory_overhead: f64,
    pub profit_margin: f64,
    pub expected_completion: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectMaterial {
    pub material_id: i64,
    pub quantity_needed: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectAccessory {
    pub name: String,
    pub accessory_type: String,
    pub quantity: i32,
    pub cost_per_unit: f64,
}

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Payment {
    pub id: i64,
    pub project_id: i64,
    pub payment_type: String, // "initial", "progress", "final"
    pub amount: f64,
    pub status: String,
    pub invoice_number: String,
    pub payment_date: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct Transaction {
    pub id: i64,
    pub description: String,
    pub transaction_type: String, // "income", "expense"
    pub category: String,
    pub amount: f64,
    pub status: String,
    pub reference: Option<String>,
    pub project_id: Option<i64>,
    pub transaction_date: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardStats {
    pub total_projects: i32,
    pub active_projects: i32,
    pub total_employees: i32,
    pub total_customers: i32,
    pub monthly_revenue: f64,
    pub monthly_expenses: f64,
    pub total_materials: i32,
    pub low_stock_materials: i32,
    pub pending_payments: f64,
    pub completed_projects_this_month: i32,
}

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct RecentActivity {
    pub id: i64,
    pub title: String,
    pub description: String,
    pub activity_type: String,
    pub created_at: DateTime<Utc>,
}

// Additional models for missing functionality

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePayment {
    pub project_id: i64,
    pub payment_type: String,
    pub amount: f64,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTransaction {
    pub description: String,
    pub transaction_type: String,
    pub category: String,
    pub amount: f64,
    pub reference: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AccountBalance {
    pub account: String,
    pub balance: f64,
    pub last_updated: DateTime<Utc>,
}
