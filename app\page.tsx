"use client"

import {
  Package,
  Users,
  Calculator,
  DollarSign,
  FileText,
  CreditCard,
  User<PERSON><PERSON>ck,
  TrendingUp,
  Activity,
  Clock,
} from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { dashboardApi, activitiesApi } from "@/lib/tauri-api"

interface DashboardStats {
  total_projects: number
  active_projects: number
  total_employees: number
  total_customers: number
  monthly_revenue: number
  monthly_expenses: number
  total_materials: number
  low_stock_materials: number
  pending_payments: number
  completed_projects_this_month: number
}

interface RecentActivity {
  id: number
  title: string
  description: string
  activity_type: string
  created_at: string
}

export default function Dashboard() {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    total_projects: 0,
    active_projects: 0,
    total_employees: 0,
    total_customers: 0,
    monthly_revenue: 0,
    monthly_expenses: 0,
    total_materials: 0,
    low_stock_materials: 0,
    pending_payments: 0,
    completed_projects_this_month: 0,
  })
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true)

        // جلب الإحصائيات من قاعدة البيانات
        const stats = await dashboardApi.getStats()
        setDashboardStats(stats)

        // جلب الأنشطة الأخيرة من قاعدة البيانات
        const activities = await activitiesApi.getRecent()
        setRecentActivities(activities)

      } catch (error) {
        console.error("خطأ في تحميل بيانات لوحة التحكم:", error)
        // في حالة الخطأ، استخدم البيانات الافتراضية
      } finally {
        setIsLoading(false)
      }
    }

    loadDashboardData()
  }, [])

  // البيانات المحدثة من قاعدة البيانات
  const stats = [
    {
      title: "المشاريع النشطة",
      value: dashboardStats.active_projects.toString(),
      description: "قيد الإنتاج حالياً",
      icon: Package,
      color: "from-blue-500 to-indigo-600",
      change: `${dashboardStats.active_projects}`,
      trend: "up",
    },
    {
      title: "إجمالي الموظفين",
      value: dashboardStats.total_employees.toString(),
      description: "القوى العاملة في المصنع",
      icon: Users,
      color: "from-emerald-500 to-teal-600",
      change: `${dashboardStats.total_employees}`,
      trend: "up",
    },
    {
      title: "الإيرادات الشهرية",
      value: `${dashboardStats.monthly_revenue.toLocaleString()} د.ل`,
      description: "أرباح الشهر الحالي",
      icon: DollarSign,
      color: "from-amber-500 to-orange-600",
      change: `${dashboardStats.monthly_revenue > 0 ? '+' : ''}${dashboardStats.monthly_revenue.toLocaleString()} د.ل`,
      trend: dashboardStats.monthly_revenue > 0 ? "up" : "down",
    },
    {
      title: "العملاء النشطون",
      value: dashboardStats.total_customers.toString(),
      description: "إجمالي العملاء المسجلين",
      icon: UserCheck,
      color: "from-purple-500 to-pink-600",
      change: `${dashboardStats.total_customers}`,
      trend: "up",
    },
  ]

  const quickActions = [
    {
      title: "حاسبة الإنتاج",
      description: "حساب تكاليف الإنتاج للمشاريع الجديدة بالمتر المربع",
      href: "/production",
      icon: Calculator,
      gradient: "from-indigo-500 to-purple-600",
    },
    {
      title: "إدارة العملاء",
      description: "نظام CRM شامل لإدارة العملاء وتتبع التفاعلات",
      href: "/crm",
      icon: UserCheck,
      gradient: "from-emerald-500 to-teal-600",
    },
    {
      title: "إدارة المواد",
      description: "إدارة المواد الخام والمخزون",
      href: "/materials",
      icon: Package,
      gradient: "from-blue-500 to-cyan-600",
    },
    {
      title: "إدارة الرواتب",
      description: "التعامل مع مدفوعات الموظفين والسجلات",
      href: "/payroll",
      icon: Users,
      gradient: "from-amber-500 to-orange-600",
    },
    {
      title: "الخزينة",
      description: "نظرة عامة مالية وتدفق نقدي بالدينار الليبي",
      href: "/treasury",
      icon: DollarSign,
      gradient: "from-green-500 to-emerald-600",
    },
    {
      title: "نظام المدفوعات",
      description: "إدارة الفواتير والمدفوعات المتدرجة",
      href: "/payments",
      icon: CreditCard,
      gradient: "from-purple-500 to-pink-600",
    },
    {
      title: "التقارير",
      description: "إنشاء تقارير تجارية مفصلة",
      href: "/reports",
      icon: FileText,
      gradient: "from-slate-500 to-gray-600",
    },
  ]

  // دالة لتحويل نوع النشاط إلى أيقونة ولون
  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'customer':
        return { icon: UserCheck, color: "text-emerald-600" }
      case 'payment':
        return { icon: DollarSign, color: "text-amber-600" }
      case 'employee':
        return { icon: Users, color: "text-blue-600" }
      case 'project':
        return { icon: Package, color: "text-purple-600" }
      case 'material':
        return { icon: Package, color: "text-orange-600" }
      default:
        return { icon: Activity, color: "text-indigo-600" }
    }
  }

  // دالة لتحويل التاريخ إلى نص نسبي
  const getRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 60) {
      return `منذ ${diffInMinutes} دقيقة`
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return `منذ ${hours} ساعة`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return `منذ ${days} يوم`
    }
  }

  // تحويل الأنشطة من قاعدة البيانات إلى تنسيق العرض
  const displayActivities = recentActivities.map(activity => {
    const { icon, color } = getActivityIcon(activity.activity_type)
    return {
      title: activity.title,
      time: getRelativeTime(activity.created_at),
      icon,
      color,
    }
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-12 text-center animate-fade-in">
          <div className="inline-flex items-center space-x-3 space-x-reverse mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
              <span className="text-white font-bold text-2xl">H</span>
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                مجموعة H
              </h1>
              <p className="text-slate-600 text-lg">إدارة مصنع الأثاث</p>
            </div>
          </div>
          <p className="text-slate-500 max-w-2xl mx-auto">
            نظام شامل لإدارة الإنتاج والأعمال مع CRM متقدم وإدارة مالية بالدينار الليبي
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {isLoading ? (
            // عرض مؤشرات التحميل للإحصائيات
            Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="modern-stat-card group animate-pulse"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-slate-200 rounded-2xl"></div>
                  <div className="w-16 h-4 bg-slate-200 rounded"></div>
                </div>
                <div>
                  <div className="w-24 h-4 bg-slate-200 rounded mb-2"></div>
                  <div className="w-16 h-8 bg-slate-200 rounded mb-2"></div>
                  <div className="w-32 h-3 bg-slate-200 rounded"></div>
                </div>
              </div>
            ))
          ) : (
            // عرض الإحصائيات الحقيقية
            stats.map((stat, index) => (
              <div
                key={index}
                className="modern-stat-card group animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-2xl bg-gradient-to-r ${stat.color} shadow-lg`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <TrendingUp className={`h-4 w-4 ${stat.trend === 'up' ? 'text-emerald-500' : 'text-red-500'}`} />
                    <span className={`text-sm font-medium ${stat.trend === 'up' ? 'text-emerald-600' : 'text-red-600'}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-slate-600 mb-1">{stat.title}</h3>
                  <div className="text-3xl font-bold text-slate-900 mb-1">{stat.value}</div>
                  <p className="text-sm text-slate-500">{stat.description}</p>
                </div>
              </div>
            ))
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-bold text-slate-900 mb-6 flex items-center space-x-2 space-x-reverse">
              <Activity className="h-6 w-6 text-indigo-600" />
              <span>الإجراءات السريعة</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {quickActions.map((action, index) => (
                <Link key={index} href={action.href}>
                  <div
                    className="modern-card group cursor-pointer animate-slide-in"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="p-6">
                      <div className="flex items-center space-x-4 space-x-reverse mb-4">
                        <div className={`p-3 rounded-2xl bg-gradient-to-r ${action.gradient} shadow-lg`}>
                          <action.icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-slate-900 group-hover:text-indigo-600 transition-colors">
                            {action.title}
                          </h3>
                        </div>
                      </div>
                      <p className="text-slate-600 text-sm leading-relaxed">{action.description}</p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-1">
            <h2 className="text-2xl font-bold text-slate-900 mb-6 flex items-center space-x-2 space-x-reverse">
              <Clock className="h-6 w-6 text-indigo-600" />
              <span>النشاط الأخير</span>
            </h2>
            <div className="modern-card">
              <div className="p-6">
                <div className="space-y-6">
                  {isLoading ? (
                    // عرض مؤشر التحميل
                    <div className="text-center py-8">
                      <div className="w-8 h-8 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                      <p className="text-slate-500 text-sm">جاري تحميل الأنشطة...</p>
                    </div>
                  ) : displayActivities.length > 0 ? (
                    // عرض الأنشطة من قاعدة البيانات
                    displayActivities.map((activity, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-4 space-x-reverse animate-fade-in"
                        style={{ animationDelay: `${index * 0.1}s` }}
                      >
                        <div className={`p-2 rounded-xl bg-slate-100 ${activity.color}`}>
                          <activity.icon className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-slate-900 leading-relaxed">{activity.title}</p>
                          <p className="text-xs text-slate-500 mt-1">{activity.time}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    // عرض رسالة عدم وجود أنشطة
                    <div className="text-center py-8">
                      <Activity className="h-12 w-12 text-slate-300 mx-auto mb-2" />
                      <p className="text-slate-500">لا توجد أنشطة حديثة</p>
                      <p className="text-slate-400 text-sm">ستظهر الأنشطة هنا عند إضافة بيانات جديدة</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
