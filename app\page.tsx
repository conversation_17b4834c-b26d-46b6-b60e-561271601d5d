export default function Dashboard() {
  return (
    <div className="min-h-screen bg-blue-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-4xl font-bold text-center text-blue-600 mb-8">
            مجموعة H - نظام إدارة مصنع الأثاث
          </h1>

          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">H</span>
            </div>
            <p className="text-gray-600 text-lg">مرحباً بك في نظام إدارة المصنع</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-blue-100 p-6 rounded-lg text-center">
              <h3 className="text-xl font-semibold text-blue-800 mb-2">إدارة العملاء</h3>
              <p className="text-blue-600">نظام CRM شامل</p>
            </div>

            <div className="bg-green-100 p-6 rounded-lg text-center">
              <h3 className="text-xl font-semibold text-green-800 mb-2">حاسبة الإنتاج</h3>
              <p className="text-green-600">حساب التكاليف</p>
            </div>

            <div className="bg-purple-100 p-6 rounded-lg text-center">
              <h3 className="text-xl font-semibold text-purple-800 mb-2">إدارة المواد</h3>
              <p className="text-purple-600">المخزون والمواد الخام</p>
            </div>

            <div className="bg-orange-100 p-6 rounded-lg text-center">
              <h3 className="text-xl font-semibold text-orange-800 mb-2">الرواتب</h3>
              <p className="text-orange-600">إدارة رواتب الموظفين</p>
            </div>

            <div className="bg-red-100 p-6 rounded-lg text-center">
              <h3 className="text-xl font-semibold text-red-800 mb-2">الخزينة</h3>
              <p className="text-red-600">الإدارة المالية</p>
            </div>

            <div className="bg-indigo-100 p-6 rounded-lg text-center">
              <h3 className="text-xl font-semibold text-indigo-800 mb-2">التقارير</h3>
              <p className="text-indigo-600">تقارير شاملة</p>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-500">التطبيق يعمل بنجاح! 🎉</p>
            <p className="text-sm text-gray-400 mt-2">جميع الحقوق محفوظة © 2024 مجموعة H</p>
          </div>
        </div>
      </div>
    </div>
  )
}
      description: "قيد الإنتاج حالياً",
      icon: Package,
      color: "from-blue-500 to-indigo-600",
      change: "+12%",
      trend: "up",
    },
    {
      title: "إجمالي الموظفين",
      value: "45",
      description: "القوى العاملة في المصنع",
      icon: Users,
      color: "from-emerald-500 to-teal-600",
      change: "+3",
      trend: "up",
    },
    {
      title: "الإيرادات الشهرية",
      value: "31,350 د.ل",
      description: "أرباح الشهر الحالي",
      icon: DollarSign,
      color: "from-amber-500 to-orange-600",
      change: "+18%",
      trend: "up",
    },
    {
      title: "العملاء النشطون",
      value: "28",
      description: "عملاء مع مشاريع جارية",
      icon: UserCheck,
      color: "from-purple-500 to-pink-600",
      change: "+5",
      trend: "up",
    },
  ]

  const quickActions = [
    {
      title: "حاسبة الإنتاج",
      description: "حساب تكاليف الإنتاج للمشاريع الجديدة بالمتر المربع",
      href: "/production",
      icon: Calculator,
      gradient: "from-indigo-500 to-purple-600",
    },
    {
      title: "إدارة العملاء",
      description: "نظام CRM شامل لإدارة العملاء وتتبع التفاعلات",
      href: "/crm",
      icon: UserCheck,
      gradient: "from-emerald-500 to-teal-600",
    },
    {
      title: "إدارة المواد",
      description: "إدارة المواد الخام والمخزون",
      href: "/materials",
      icon: Package,
      gradient: "from-blue-500 to-cyan-600",
    },
    {
      title: "إدارة الرواتب",
      description: "التعامل مع مدفوعات الموظفين والسجلات",
      href: "/payroll",
      icon: Users,
      gradient: "from-amber-500 to-orange-600",
    },
    {
      title: "الخزينة",
      description: "نظرة عامة مالية وتدفق نقدي بالدينار الليبي",
      href: "/treasury",
      icon: DollarSign,
      gradient: "from-green-500 to-emerald-600",
    },
    {
      title: "نظام المدفوعات",
      description: "إدارة الفواتير والمدفوعات المتدرجة",
      href: "/payments",
      icon: CreditCard,
      gradient: "from-purple-500 to-pink-600",
    },
    {
      title: "التقارير",
      description: "إنشاء تقارير تجارية مفصلة",
      href: "/reports",
      icon: FileText,
      gradient: "from-slate-500 to-gray-600",
    },
  ]

  const recentActivities = [
    {
      title: "تم إضافة عميل جديد: شركة الأثاث الحديث",
      time: "منذ ساعة واحدة",
      icon: UserCheck,
      color: "text-emerald-600",
    },
    {
      title: "انتقال فاتورة إلى مرحلة التصنيع - تم تسجيل دفعة 70%",
      time: "منذ ساعتين",
      icon: DollarSign,
      color: "text-amber-600",
    },
    {
      title: "تم تخصيص مشروع جديد للموظف أحمد محمد السالم",
      time: "منذ 3 ساعات",
      icon: Users,
      color: "text-blue-600",
    },
    {
      title: "تم إكمال طلب الإنتاج #PO-2024-001",
      time: "منذ 4 ساعات",
      icon: Package,
      color: "text-purple-600",
    },
    {
      title: "تحديث سجل تفاعل العميل: عائلة الأحمد",
      time: "منذ 5 ساعات",
      icon: Activity,
      color: "text-indigo-600",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-12 text-center animate-fade-in">
          <div className="inline-flex items-center space-x-3 space-x-reverse mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
              <span className="text-white font-bold text-2xl">H</span>
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                مجموعة H
              </h1>
              <p className="text-slate-600 text-lg">إدارة مصنع الأثاث</p>
            </div>
          </div>
          <p className="text-slate-500 max-w-2xl mx-auto">
            نظام شامل لإدارة الإنتاج والأعمال مع CRM متقدم وإدارة مالية بالدينار الليبي
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="modern-stat-card group animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-2xl bg-gradient-to-r ${stat.color} shadow-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <TrendingUp className="h-4 w-4 text-emerald-500" />
                  <span className="text-sm font-medium text-emerald-600">{stat.change}</span>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-slate-600 mb-1">{stat.title}</h3>
                <div className="text-3xl font-bold text-slate-900 mb-1">{stat.value}</div>
                <p className="text-sm text-slate-500">{stat.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-bold text-slate-900 mb-6 flex items-center space-x-2 space-x-reverse">
              <Activity className="h-6 w-6 text-indigo-600" />
              <span>الإجراءات السريعة</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {quickActions.map((action, index) => (
                <Link key={index} href={action.href}>
                  <div
                    className="modern-card group cursor-pointer animate-slide-in"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="p-6">
                      <div className="flex items-center space-x-4 space-x-reverse mb-4">
                        <div className={`p-3 rounded-2xl bg-gradient-to-r ${action.gradient} shadow-lg`}>
                          <action.icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-slate-900 group-hover:text-indigo-600 transition-colors">
                            {action.title}
                          </h3>
                        </div>
                      </div>
                      <p className="text-slate-600 text-sm leading-relaxed">{action.description}</p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-1">
            <h2 className="text-2xl font-bold text-slate-900 mb-6 flex items-center space-x-2 space-x-reverse">
              <Clock className="h-6 w-6 text-indigo-600" />
              <span>النشاط الأخير</span>
            </h2>
            <div className="modern-card">
              <div className="p-6">
                <div className="space-y-6">
                  {recentActivities.map((activity, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-4 space-x-reverse animate-fade-in"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <div className={`p-2 rounded-xl bg-slate-100 ${activity.color}`}>
                        <activity.icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-slate-900 leading-relaxed">{activity.title}</p>
                        <p className="text-xs text-slate-500 mt-1">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
