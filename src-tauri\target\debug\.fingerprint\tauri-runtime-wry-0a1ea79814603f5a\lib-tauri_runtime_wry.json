{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 12875508615657262440, "deps": [[376837177317575824, "softbuffer", false, 5675920424439530002], [442785307232013896, "tauri_runtime", false, 12737817405569267810], [3150220818285335163, "url", false, 8378097199966561882], [3722963349756955755, "once_cell", false, 1536878428866660243], [4143744114649553716, "raw_window_handle", false, 3760476461987929444], [5986029879202738730, "log", false, 14415701566817692790], [7752760652095876438, "build_script_build", false, 8450531002371937593], [8539587424388551196, "webview2_com", false, 2612316728963077255], [9010263965687315507, "http", false, 12211119702532877820], [11050281405049894993, "tauri_utils", false, 15157767861222970160], [13116089016666501665, "windows", false, 4842511405203577448], [13223659721939363523, "tao", false, 3223958696899823322], [14794439852947137341, "wry", false, 12893658942542463168]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-0a1ea79814603f5a\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}