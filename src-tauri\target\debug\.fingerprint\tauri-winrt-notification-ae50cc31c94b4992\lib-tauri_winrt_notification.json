{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 8514343674982384859, "deps": [[1462335029370885857, "quick_xml", false, 18445969611304319329], [3334271191048661305, "windows_version", false, 13701480444880781506], [10806645703491011684, "thiserror", false, 1911401726845299826], [13116089016666501665, "windows", false, 4842511405203577448]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-ae50cc31c94b4992\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}