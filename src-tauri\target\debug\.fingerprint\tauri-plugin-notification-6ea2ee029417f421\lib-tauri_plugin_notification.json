{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 15657897354478470176, "path": 12279867945861181210, "deps": [[947818755262499932, "notify_rust", false, 5160599578674460365], [3150220818285335163, "url", false, 8378097199966561882], [5986029879202738730, "log", false, 14415701566817692790], [9689903380558560274, "serde", false, 8732212302608731374], [10755362358622467486, "tauri", false, 1815845651195265658], [10806645703491011684, "thiserror", false, 1911401726845299826], [12409575957772518135, "time", false, 7583645742898373533], [12783828711503588811, "build_script_build", false, 9825508235357264756], [12986574360607194341, "serde_repr", false, 11233520184535608381], [13208667028893622512, "rand", false, 12376982049844976056], [15367738274754116744, "serde_json", false, 6491859613021625460]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-6ea2ee029417f421\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}