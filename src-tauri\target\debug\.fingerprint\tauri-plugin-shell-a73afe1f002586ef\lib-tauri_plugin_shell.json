{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 8213231278302215618, "deps": [[500211409582349667, "shared_child", false, 2796868643868191758], [1582828171158827377, "build_script_build", false, 8276984117680058246], [5986029879202738730, "log", false, 14415701566817692790], [9451456094439810778, "regex", false, 6795744845245670884], [9538054652646069845, "tokio", false, 13844825867709392792], [9689903380558560274, "serde", false, 8732212302608731374], [10755362358622467486, "tauri", false, 1815845651195265658], [10806645703491011684, "thiserror", false, 1911401726845299826], [11337703028400419576, "os_pipe", false, 16361021715673334581], [14564311161534545801, "encoding_rs", false, 478095620073393826], [15367738274754116744, "serde_json", false, 6491859613021625460], [16192041687293812804, "open", false, 1647320476627264493]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-a73afe1f002586ef\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}